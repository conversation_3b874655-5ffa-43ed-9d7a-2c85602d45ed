"""
Enhanced MCP Manager with Bedrock Session Management

Async-first mixin aligned with Bedrock-backed session_manager integration.
- Uses ChatSession for context/history
- Builds valid Converse toolConfig (JSON schema object)
- Persists turns to Bedrock via ChatSession.add_turn (which writes invocation steps)
"""

import logging
import os
import json
from typing import Dict, List, Any, Optional
from session_manager_new import session_manager

logger = logging.getLogger(__name__)

class EnhancedMCPMixin:
    """
    Mixin class to add Bedrock session-aware functionality to existing MCPClientManager.
    Uses native Bedrock session management (via session_manager) for context retention.
    """
    
    # Default; manager overrides from env
    model_id: str = "apac.amazon.nova-lite-v1:0"
    
    async def chat_with_bedrock_with_context(
        self,
        message: str,
        session_id: str,
        tools_available: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """
        Enhanced Bedrock chat with native session context retention using session_manager.
        """
        try:
            chat_session = session_manager.get_or_create_session(session_id)
            historical_messages = chat_session.get_bedrock_messages(max_turns=8)
            
            current_messages = historical_messages + [{
                "role": "user",
                "content": [{"text": message}]
            }]
            
            system_message = self._build_context_aware_system_message(chat_session, tools_available)
            tool_config = self._build_tool_config_for_bedrock(tools_available)
            
            result = await self._execute_contextual_conversation(
                messages=current_messages,
                system_message=system_message,
                tool_config=tool_config,
                session_id=session_id,
                model_id=self.model_id,
            )
            
            chat_session.add_turn(
                user_message=message,
                assistant_response=result["response"],
                tools_used=result.get("tools_used", []),
            )
            
            logger.info(f"Completed contextual chat for session {session_id}: {len(result.get('tools_used', []))} tools used")
            
            return {
                "response": result["response"],
                "tools_used": result.get("tools_used", []),
                "session_id": session_id
            }
            
        except Exception as e:
            error_msg = str(e)
            logger.error(f"Error in contextual chat for session {session_id}: {error_msg}")
            
            # Enhanced error handling
            if "ValidationException" in error_msg:
                if "model" in error_msg.lower():
                    response_text = f"Model validation error. Please check your BEDROCK_MODEL_ID in .env file. Current model: {self.model_id}"
                elif "region" in error_msg.lower():
                    response_text = f"Region validation error. Please check your AWS_REGION configuration. Current region: {os.getenv('AWS_REGION', 'not set')}"
                else:
                    response_text = f"Bedrock API validation error: {error_msg}"
            elif "RetryError" in error_msg:
                response_text = "Connection retry failed. Please check AWS credentials and region configuration."
            elif "AccessDenied" in error_msg:
                response_text = "Access denied. Please check your AWS permissions for Bedrock services."
            elif "ResourceNotFound" in error_msg:
                response_text = f"Resource not found. Please verify your model ID: {self.model_id}"
            else:
                response_text = f"I apologize, but I encountered an error: {error_msg}"
            
            return {
                "response": response_text,
                "tools_used": [],
                "session_id": session_id,
                "error": True
            }

    def _build_context_aware_system_message(
        self,
        chat_session,
        tools_available: Optional[List[str]] = None
    ) -> str:
        """Build context-aware system message with defensive check."""
        context = ""
        if hasattr(chat_session, "get_context_for_bedrock") and callable(chat_session.get_context_for_bedrock):
            context = chat_session.get_context_for_bedrock()
        
        tool_hint = ""
        if tools_available:
            tool_hint = (
                "\nYou have access to tools and should use them iteratively when needed. "
                "You can batch independent tool calls in parallel. "
                "Continue using tools until you have all the information needed for a complete answer. "
                "Only stop when you are confident you can provide a final, comprehensive response."
            )
        
        system = (
            "You are an assistant inside an MCP Bot. "
            "Answer precisely and use tools iteratively to gather complete information."
            f"\n\nSession Context:\n{context}"
            f"{tool_hint}"
        )
        
        return system

    def _build_tool_config_for_bedrock(self, tools_available: Optional[List[str]] = None) -> Optional[Dict]:
        """Build tool configuration for Bedrock."""
        if not tools_available:
            return None
        
        available_tools = self.get_available_tools()
        tools: List[Dict[str, Any]] = []
        
        for tool_key in tools_available:
            if tool_key not in available_tools:
                continue
            
            tool_data = available_tools[tool_key]
            tool = tool_data["tool"]
            
            input_schema = tool.get("input_schema") or {"type": "object", "properties": {}, "required": []}
            
            if "type" not in input_schema:
                input_schema["type"] = "object"
            if "properties" not in input_schema:
                input_schema["properties"] = {}
            
            tools.append({
                "toolSpec": {
                    "name": tool["name"],
                    "description": tool.get("description") or f"Tool from server {tool_data['server']}",
                    "inputSchema": {"json": input_schema}
                }
            })
        
        if not tools:
            return None
        
        return {"tools": tools, "toolChoice": {"auto": {}}}

    async def _execute_contextual_conversation(
        self,
        messages: List[Dict[str, Any]],
        system_message: str,
        tool_config: Optional[Dict],
        session_id: str,
        model_id: str,
    ) -> Dict[str, Any]:
        """Execute contextual conversation with multi-iteration tool loop."""
        runtime = await self.get_async_bedrock_runtime()
        inference_config = {"temperature": 0.4, "topP": 0.9}
        
        msgs = list(messages)
        tools_used: List[Dict[str, Any]] = []
        max_iterations = 15  # prevent infinite loops
        
        for iteration in range(max_iterations):
            try:
                req = {
                    "modelId": model_id,
                    "messages": msgs,
                    "system": [{"text": system_message}],
                    "inferenceConfig": inference_config,
                }
                
                if tool_config:
                    req["toolConfig"] = tool_config
                
                resp = await runtime.converse(**req)
                output = resp.get("output", {}).get("message", {})
                content = output.get("content", [])
                stop_reason = resp.get("stopReason")
                
                logger.debug(f"Iteration {iteration + 1}: stop_reason={stop_reason}")
                
                if stop_reason == "tool_use":
                    # Record assistant toolUse
                    msgs.append({"role": "assistant", "content": content})
                    
                    # Execute tool calls in parallel
                    planned_calls = [
                        {
                            "name": b["toolUse"]["name"],
                            "input": b["toolUse"].get("input", {}),
                            "toolUseId": b["toolUse"].get("toolUseId"),
                        }
                        for b in content
                        if "toolUse" in b
                    ]
                    
                    exec_results = await self._execute_tool_calls(planned_calls, session_id)
                    tools_used.extend(exec_results)
                    
                    # Provide toolResult blocks back to model as JSON
                    tool_result_blocks = []
                    for call, res in zip(planned_calls, exec_results):
                        payload = {"success": res.get("success", False)}
                        
                        if res.get("success"):
                            result_data = res.get("result", "")
                            try:
                                parsed = json.loads(result_data) if isinstance(result_data, str) else result_data
                                payload["result"] = parsed
                            except Exception:
                                payload["result"] = result_data
                        else:
                            payload["error"] = res.get("error", "Unknown error")
                        
                        tool_result_blocks.append({
                            "toolResult": {
                                "toolUseId": call["toolUseId"],
                                "content": [{"json": payload}]
                            }
                        })
                    
                    msgs.append({"role": "user", "content": tool_result_blocks})
                    continue
                
                elif stop_reason in ("end_turn", "stop_sequence", "max_tokens"):
                    final_text_parts = [b["text"] for b in content if "text" in b]
                    final_text = "\n".join(final_text_parts).strip() if final_text_parts else ""
                    # Filter out thinking content before returning
                    filtered_text = self._filter_thinking_content(final_text)
                    return {"response": filtered_text, "tools_used": tools_used, "session_id": session_id}

                else:
                    final_text_parts = [b["text"] for b in content if "text" in b]
                    final_text = "\n".join(final_text_parts).strip() if final_text_parts else f"Response completed with stop reason: {stop_reason}"
                    # Filter out thinking content before returning
                    filtered_text = self._filter_thinking_content(final_text)
                    return {"response": filtered_text, "tools_used": tools_used, "session_id": session_id}
                    
            except Exception as e:
                logger.error(f"Error in conversation iteration {iteration + 1}: {e}")
                if iteration == 0:  # If first iteration fails, return error
                    raise
                # Otherwise, return what we have so far
                return {"response": f"Partial response due to error: {str(e)}", "tools_used": tools_used, "session_id": session_id}
        
        return {"response": "Response completed after maximum iterations.", "tools_used": tools_used, "session_id": session_id}

    def _filter_thinking_content(self, text: str) -> str:
        """
        Filter out thinking content from model responses.
        Removes content between <thinking> and </thinking> tags and other reasoning patterns.
        """
        import re

        if not text:
            return text

        # Remove thinking tags and their content
        # This pattern matches <thinking>...</thinking> including multiline content
        thinking_pattern = r'<thinking>.*?</thinking>'
        filtered_text = re.sub(thinking_pattern, '', text, flags=re.DOTALL | re.IGNORECASE)

        # Also remove other common reasoning patterns that might leak through
        # Remove content between <reasoning> and </reasoning> tags
        reasoning_pattern = r'<reasoning>.*?</reasoning>'
        filtered_text = re.sub(reasoning_pattern, '', filtered_text, flags=re.DOTALL | re.IGNORECASE)

        # Remove content between <analysis> and </analysis> tags
        analysis_pattern = r'<analysis>.*?</analysis>'
        filtered_text = re.sub(analysis_pattern, '', filtered_text, flags=re.DOTALL | re.IGNORECASE)

        # Clean up any extra whitespace that might be left
        filtered_text = re.sub(r'\n\s*\n\s*\n', '\n\n', filtered_text)  # Replace multiple newlines with double newlines
        filtered_text = filtered_text.strip()

        return filtered_text

    async def get_async_bedrock_runtime(self):
        """Get async bedrock runtime client."""
        try:
            import aioboto3
        except ImportError:
            raise ImportError("aioboto3 is required for async Bedrock operations. Install with: pip install aioboto3")
        
        session = aioboto3.Session()
        client = session.client("bedrock-runtime")
        return await client.__aenter__()

    async def _execute_tool_calls(self, tool_calls: List[Dict], session_id: str) -> List[Dict]:
        """Execute tool calls with enhanced error handling."""
        tools_used = []
        
        for tool_call in tool_calls:
            tool_name = tool_call.get("name")
            tool_input = tool_call.get("input", {})
            tool_use_id = tool_call.get("toolUseId")
            
            logger.info(f"Executing tool: {tool_name} with input: {tool_input}")
            
            server_name = self._find_server_for_tool(tool_name)
            
            if not server_name:
                tools_used.append({
                    "tool_name": tool_name,
                    "server_name": None,
                    "input": tool_input,
                    "success": False,
                    "error": f"Tool {tool_name} not found",
                    "session_id": session_id,
                    "toolUseId": tool_use_id,
                })
                continue
            
            try:
                result = await self.call_tool(server_name, tool_name, tool_input)
                
                usage = {
                    "tool_name": tool_name,
                    "server_name": server_name,
                    "input": tool_input,
                    "success": result.get("success", False),
                    "session_id": session_id,
                    "toolUseId": tool_use_id,
                }
                
                if usage["success"]:
                    usage["result"] = str(result.get("result", ""))
                else:
                    usage["error"] = result.get("error", "Unknown error")
                
                tools_used.append(usage)
                
            except Exception as e:
                logger.error(f"Tool execution exception for {tool_name}: {e}")
                tools_used.append({
                    "tool_name": tool_name,
                    "server_name": server_name,
                    "input": tool_input,
                    "success": False,
                    "error": f"Execution exception: {str(e)}",
                    "session_id": session_id,
                    "toolUseId": tool_use_id,
                })
        
        return tools_used

    def _find_server_for_tool(self, tool_name: str) -> Optional[str]:
        """Find server that provides a tool."""
        available_tools = self.get_available_tools()
        for data in available_tools.values():
            if data["tool"]["name"] == tool_name:
                return data["server"]
        return None
